#!/bin/bash

# Navigate to the chapters directory
cd /Users/<USER>/Projects/ObsidianSpire/01_Book-The_Obsidian_Spire_Queendom/chapters

# Loop through all numbered chapter files
for file in [0-9]*_*.md; do
  # Extract the chapter number by removing leading zeros and everything after the first underscore
  chapter=$(echo "$file" | sed -E 's/^0*([0-9]+)_.*/\1/')
  
  # Ensure the target directory exists
  mkdir -p "1/$chapter"
  
  # Move the file to its corresponding directory
  echo "Moving $file to 1/$chapter/"
  cp "$file" "1/$chapter/"
done

echo "All chapters moved successfully!"
