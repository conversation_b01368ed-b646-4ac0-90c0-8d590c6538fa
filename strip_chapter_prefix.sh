#!/bin/bash

# <PERSON><PERSON>t to strip the number and underscore prefix from chapter files
BASE_DIR="/Users/<USER>/Projects/ObsidianSpire/01_Book-The_Obsidian_Spire_Queendom/chapters"

# Find all markdown files with pattern [number]_*.md
find "$BASE_DIR" -type f -name "[0-9]*_*.md" | while read -r file; do
    # Get the directory path
    dir=$(dirname "$file")
    
    # Get the filename
    filename=$(basename "$file")
    
    # Remove the number and underscore prefix (e.g., 01_, 27_, etc.)
    new_filename=$(echo "$filename" | sed -E 's/^[0-9]+_//')
    
    # Create the new file path
    new_file="$dir/$new_filename"
    
    # Rename the file
    mv "$file" "$new_file"
    echo "Renamed: $filename -> $new_filename"
done

echo "All chapter files have been renamed."
