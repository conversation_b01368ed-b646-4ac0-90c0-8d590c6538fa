#!/bin/zsh
COMMAND="$1"
BOOKS_BASE_DIR="books"
BOOK_NUM="${2:-1}"

SCRIPTS_DIR="$(dirname "$(realpath "$0")")/scripts"

# If the book input is an integer, then expand it to a directory
if [[ "$BOOK_NUM" =~ ^[1-9]+$ ]]; then
    if [ "$BOOK_NUM" -lt 10 ]; then
        BOOK_NUM="0$BOOK_NUM"
    fi
    # Find the book directory in the books/ directory
    BOOK_DIR=$(ls -d "$BOOKS_BASE_DIR"/"$BOOK_NUM"_*)
else
    # If a full directory name was provided
    BOOK_DIR="$BOOKS_BASE_DIR/$BOOK_NUM"
fi

# Extract the book name from the directory path
BOOK_NAME=$(basename "${BOOK_DIR}" | sed 's/^[0-9]*_Book-//')
OUTPUT_PDF="${BOOK_NAME}.pdf"
OUTPUT_MD="${BOOK_NAME}.md"
CHAPTER_DIR="${BOOK_DIR}/chapters"

function run_script {
    local script="$1"
    shift
    if [ -f "$SCRIPTS_DIR/$script.py" ]; then
        # Check if uv is available, otherwise use python directly
        if command -v uv &> /dev/null; then
            uv run $SCRIPTS_DIR/"$script".py "$@"
        else
            python3 $SCRIPTS_DIR/"$script".py "$@"
        fi
    elif [ -f "$SCRIPTS_DIR/$script.sh" ]; then
        zsh $SCRIPTS_DIR/"$script".sh "$@"
    else
        echo "Script $script not found in $SCRIPTS_DIR"
        exit 1
    fi
}

case "$COMMAND" in
    "wc")
        echo "Counting words in the book..."
        run_script word_count "$CHAPTER_DIR"
        ;;

    "mb")
        echo "Generating Markdown book..."
        run_script clean_chapters "$CHAPTER_DIR"
        run_script make_book "$CHAPTER_DIR" "$OUTPUT_MD"
        code "$OUTPUT_MD"
        ;;

    "rc")
        echo "Renaming chapters..."
        run_script rename_chapters "$CHAPTER_DIR"
        ;;
    "cc")
        echo "Cleaning chapters..."
        run_script clean_chapters "$CHAPTER_DIR"
        ;;
    "cp")
        echo "Create a PDF from the book..."
        run_script create_pdf "$OUTPUT_MD" "$OUTPUT_PDF"
        code "$OUTPUT_PDF"
        ;;
    "mp")
        echo "Generating Markdown book..."
        run_script clean_chapters "$CHAPTER_DIR"
        run_script make_book "$CHAPTER_DIR" "$OUTPUT_MD"
        echo "Making a PDF from the book..."
        run_script create_pdf "$OUTPUT_MD" "$OUTPUT_PDF"
        code "$OUTPUT_PDF"
        ;;
    "ls")
        echo "Available books:"
        ls -1 books/
        ;;
    *)
        run_script "$COMMAND" "$BOOK_DIR"
        exit 1
        ;;
esac

