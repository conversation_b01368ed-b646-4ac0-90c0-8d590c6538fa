{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Book-1 Ingestion and Outline Demo\n", "\n", "This notebook demonstrates how to ingest all chapters from `Book-1`, generate per-chapter outlines, and create a hierarchical book outline using the `book_writer.py` utilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# If running in Colab or a new environment, install dependencies first:\n", "# !pip install openai pyyaml python-dotenv\n", "\n", "import sys\n", "import os\n", "sys.path.append(os.path.abspath('.'))  # Ensure current directory is in path\n", "\n", "from book_writer import BookArchitect, ingest_and_outline_book"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up environment (loads .env automatically if book_writer.py uses dotenv)\n", "book_dir = 'Book-1'\n", "architect = BookArchitect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ingest Book-1 and generate outlines\n", "result = ingest_and_outline_book(book_dir, architect)\n", "chapter_outlines = result['chapters']\n", "book_outline = result['book_outline']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Per-Chapter Outlines"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pprint\n", "for chapter in chapter_outlines:\n", "    print(f\"\\n--- Chapter {chapter['number']}: {chapter['title']} ---\")\n", "    pprint.pprint(chapter['outline'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hierarchical Book Outline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(book_outline)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}