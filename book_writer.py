from openai import OpenAI
from dotenv import load_dotenv
load_dotenv()
import os
import json

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=os.getenv("OPENROUTER_API_KEY") or os.getenv("OPENAI_API_KEY")
)

class BookFactory:
    def __init__(self):
        self.model = "nousresearch/optimus-alpha"
        self.provider = os.getenv("LLM_PROVIDER", "openrouter")  # 'groq', 'gemini', or 'openrouter'
        self.groq_model = "groq/llama3-70b-8192"
        self.gemini_model = "google/gemini-2.5-pro-exp-03-25:free"

def ingest_book_directory(book_dir: str) -> dict:
    """
    Ingests a book directory (e.g., Book-1), reading all chapters and creating a hierarchical outline.
    Returns a dict: {'chapters': [ {'number': int, 'title': str, 'content': str, 'filename': str}, ... ]}
    """
    import re
    import os
    import glob
    import time
    
    start_time = time.time()
    print(f"Scanning directory: {book_dir} for chapter files...")
    chapter_files = sorted(glob.glob(os.path.join(book_dir, '*.md')))
    scan_time = time.time() - start_time
    print(f"Found {len(chapter_files)} chapter files. (Took {scan_time:.2f} seconds)")
    
    outline = {'chapters': []}
    total_chars = 0
    
    for i, filepath in enumerate(chapter_files):
        file_start_time = time.time()
        print(f"Processing file {i+1}/{len(chapter_files)}: {os.path.basename(filepath)}")
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # Extract title from first non-empty line
        for line in lines:
            if line.strip():
                title_line = line.strip()
                break
        else:
            title_line = os.path.basename(filepath)
        # Parse chapter number and title
        m = re.match(r'#?\s*Chapter\s*(\d+):?\s*(.*)', title_line)
        if m:
            number = int(m.group(1))
            title = m.group(2).strip() or os.path.basename(filepath)
        else:
            number = None
            title = os.path.basename(filepath)
        content = ''.join(lines).strip()
        total_chars += len(content)
        outline['chapters'].append({
            'number': number,
            'title': title,
            'content': content,
            'filename': os.path.basename(filepath)
        })
        file_time = time.time() - file_start_time
        print(f"  Processed {len(content)} characters in {file_time:.2f} seconds")
    
    total_time = time.time() - start_time
    print(f"Completed ingesting {len(outline['chapters'])} chapters ({total_chars} total characters) from {book_dir}")
    print(f"Total ingestion time: {total_time:.2f} seconds ({total_chars/total_time:.0f} chars/sec)")
    
    return outline

# End of ingest_book_directory

class BookGenerator:
    def __init__(self):
        self.token_counter = 0
        self.model = None
        self.context_window = 128000  # Token limit
        self.cost_rate = 0.0000035  # $0.035 per million tokens
        
    def select_model(self, task_complexity: str = "complex"):
        """Always return optimus-alpha as the model."""
        self.model = "openrouter/optimus-alpha"
        return self.model
        
    def _track_tokens(self, usage):
        """Track token usage for cost estimation"""
        self.token_counter += getattr(usage, 'prompt_tokens', 0)
        self.token_counter += getattr(usage, 'completion_tokens', 0)
        
    def generate(self, prompt: str, system_msg: str = "", max_tokens=8000, task_complexity: str = "complex", temperature=0.65) -> str:
        """Base generation with context management and dynamic model selection"""
        model = self.select_model(task_complexity)
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_msg},
                {"role": "user", "content": prompt}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )
        self._track_tokens(response.usage)
        return response.choices[0].message.content

    def create_outline(self, notes: dict) -> dict:
        """Generate book structure using Ultra-Long techniques"""
        system_msg = """You're a professional novelist. Create a detailed outline with:
        - 5 Act structure
        - Chapter word targets (1500-2500 words)
        - Character arcs
        - Key scene placement
        - Theme integration
        
        IMPORTANT: Your response must be valid JSON format."""
        
        # If notes is a string, try to parse it as JSON, otherwise use it directly
        if isinstance(notes, str):
            try:
                notes_content = notes
            except:
                notes_content = notes
        else:
            notes_content = json.dumps(notes, indent=2)
        
        prompt = f"""Transform these notes into a book outline in valid JSON format:
        {notes_content}
        
        Include scene progression tracking and pacing markers.
        
        IMPORTANT: Your response must be valid JSON format. Wrap all keys and string values in double quotes.
        Example format:
        {{
          "acts": [
            {{
              "name": "Act 1: Setup",
              "chapters": [...]
            }}
          ]
        }}
        """
        
        response = self.generate(prompt, system_msg)
        
        # Try to parse the response as JSON
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            print(f"Error: Failed to parse LLM response as JSON: {str(e)}")
            print("Response content:")
            print(response[:500] + "..." if len(response) > 500 else response)
            
            # Return a simple dict as fallback
            return {
                "error": "Failed to parse LLM response as JSON",
                "outline": response
            }

    def expand_section(self, outline: dict, section: dict) -> str:
        """Hierarchical expansion with memory management"""
        context = f"""Current Outline:
        {json.dumps(outline, indent=2)}
        
        Previous Content:
        {section.get('draft', '')}"""
        
        prompt = f"""Expand this section to {section['target_words']} words:
        Title: {section['title']}
        Key Elements: {section['elements']}
        Required Plot Points: {section['plot_points']}
        
        Add:
        - Sensory details (smells, textures, sounds)
        - Character internal monologues
        - Environmental storytelling"""
        
        return self.generate(prompt, context)

    def continuity_check(self, content: str, context: str) -> dict:
        """Ensure consistency with previous content"""
        system_msg = """Analyze text for continuity errors in:
        - Character descriptions
        - Setting details
        - Timeline consistency
        - Magic system rules"""
        
        prompt = f"""Current Content:
        {content}
        
        Story Context:
        {context}
        
        List any inconsistencies (max 3) or say 'Consistent'"""
        
        return self.generate(prompt, system_msg, max_tokens=1000)

    def estimate_cost(self) -> float:
        """Calculate total cost based on OpenRouter pricing"""
        return self.token_counter * self.cost_rate

# Example Usage
writing_notes = {
    "theme": "Power dynamics in matriarchal societies",
    "characters": {
        "Lyra": "Exiled healer with latent magical abilities",
        "Cerys": "Guard commander turned revolutionary",
        "Vorna": "Immortal matriarch draining island's life force"
    },
    "key_scenes": [
        "Lyra's bonding ritual with Aethelgard",
        "Cerys' betrayal of Vorna",
        "Torin's magical birth sequence"
    ],
    "world_elements": {
        "Aethelgard": "Sentient volcanic prison island",
        "Obsidian Spire": "Ancient seat of magical power"
    }
}

factory = BookFactory()
generator = BookGenerator() # Instantiate BookGenerator

# Phase 1: Outline Generation
book_outline = generator.create_outline(writing_notes)
print(f"Generated Outline: {json.dumps(book_outline, indent=2)}")

# Phase 2: Content Generation
sample_section = {
    "title": "The Bonding Ritual",
    "target_words": 1800,
    "elements": ["Lyra", "Aethelgard", "Island Magic"],
    "plot_points": [
        "First connection with island consciousness",
        "Discovery of latent powers",
        "Foreshadowing of Vorna's corruption"
    ]
}

draft_content = generator.expand_section(book_outline, sample_section)
continuity_report = generator.continuity_check(draft_content, json.dumps(book_outline))

print(f"\nFirst Draft ({len(draft_content.split())} words):")
print(draft_content[:500] + "...")
print(f"\nContinuity Check: {continuity_report}")
print(f"Estimated Cost: ${generator.estimate_cost():.4f}")

def ingest_and_outline_book(book_dir: str, generator: BookGenerator) -> dict:
    """
    Ingests a book directory, generates an outline for each chapter, then a hierarchical outline over all chapters.
    Returns a dict with chapter outlines and a book-level outline, compatible with BookArchitect/BookFactory.
    """
    import time
    
    overall_start_time = time.time()
    print(f"\n=== Starting book ingestion and outline generation for {book_dir} ===\n")
    
    # Step 1: Ingest book directory
    print("Step 1: Ingesting book directory...")
    step1_start = time.time()
    chapters_data = ingest_book_directory(book_dir)
    step1_time = time.time() - step1_start
    print(f"Step 1 completed in {step1_time:.2f} seconds")
    
    # Step 2: Generate outlines for each chapter
    print(f"\nStep 2: Generating outlines for {len(chapters_data['chapters'])} chapters...")
    step2_start = time.time()
    chapter_outlines = []
    total_outline_chars = 0
    
    for i, chapter in enumerate(chapters_data['chapters']):
        chapter_title = f"Chapter {chapter['number']}: {chapter['title']}" if chapter['number'] else chapter['title']
        chapter_start = time.time()
        print(f"  Processing {i+1}/{len(chapters_data['chapters'])}: {chapter_title}")
        
        # Generate outline for each chapter using the LLM
        print(f"    Generating outline... (this may take a moment)")
        outline_start = time.time()
        chapter_outline = generator.create_outline(chapter['content'])
        outline_time = time.time() - outline_start
        outline_str = str(chapter_outline)
        total_outline_chars += len(outline_str)
        
        print(f"    Outline generated: {len(outline_str)} characters in {outline_time:.2f} seconds")
        print(f"    ({len(outline_str)/outline_time:.1f} chars/sec)")
        
        chapter_outlines.append({
            'number': chapter['number'],
            'title': chapter['title'],
            'filename': chapter['filename'],
            'outline': chapter_outline
        })
        
        chapter_time = time.time() - chapter_start
        print(f"    Chapter processing completed in {chapter_time:.2f} seconds")
    
    step2_time = time.time() - step2_start
    avg_time_per_chapter = step2_time / len(chapters_data['chapters']) if chapters_data['chapters'] else 0
    print(f"Step 2 completed in {step2_time:.2f} seconds")
    print(f"Average time per chapter: {avg_time_per_chapter:.2f} seconds")
    
    # Step 3: Create hierarchical book outline
    print("\nStep 3: Creating hierarchical book outline...")
    step3_start = time.time()
    book_outline_prompt = """Given the following chapter outlines, create a hierarchical book outline with acts/parts if appropriate, and a summary for each chapter. Structure the output for use in BookArchitect/BookFactory."""
    outlines_text = '\n\n'.join([f"Chapter {c['number']}: {c['title']}\n{c['outline']}" for c in chapter_outlines])
    
    print(f"  Generating book-level outline... (this may take a moment)")
    outline_start = time.time()
    book_outline = generator.generate(f"{book_outline_prompt}\n\n{outlines_text}", temperature=0.5, max_tokens=4096)
    outline_time = time.time() - outline_start
    
    print(f"  Book outline generated: {len(book_outline)} characters in {outline_time:.2f} seconds")
    print(f"  ({len(book_outline)/outline_time:.1f} chars/sec)")
    
    step3_time = time.time() - step3_start
    print(f"Step 3 completed in {step3_time:.2f} seconds")
    
    # Overall timing
    overall_time = time.time() - overall_start_time
    print(f"\n=== Book ingestion and outline generation complete ===")
    print(f"Total processing time: {overall_time:.2f} seconds")
    print(f"  - Ingestion: {step1_time:.2f} seconds ({(step1_time/overall_time)*100:.1f}%)")
    print(f"  - Chapter outlines: {step2_time:.2f} seconds ({(step2_time/overall_time)*100:.1f}%)")
    print(f"  - Book outline: {step3_time:.2f} seconds ({(step3_time/overall_time)*100:.1f}%)")
    print(f"Total characters generated: {total_outline_chars + len(book_outline)}")
    print(f"Overall generation speed: {(total_outline_chars + len(book_outline))/overall_time:.1f} chars/sec\n")
    
    return {
        'chapters': chapter_outlines,
        'book_outline': book_outline,
        'timing': {
            'total_seconds': overall_time,
            'ingestion_seconds': step1_time,
            'chapter_outlines_seconds': step2_time,
            'book_outline_seconds': step3_time,
            'chars_per_second': (total_outline_chars + len(book_outline))/overall_time
        }
    }

# --- BookArchitect: YAML-driven, modular, LLM-agnostic ---
import yaml
from typing import Dict, List
from pprint import pprint


class BookArchitect:
    def __init__(self, client=None, provider=None):
        self.outline_template = """
        narrative_structure:
          - part: "Setup"
            chapters: []
          - part: "Rising Action"
            chapters: []
          - part: "Climax"
            chapters: []
          - part: "Falling Action"
            chapters: []
          - part: "Resolution"
            chapters: []
        
        story_elements:
          characters: []
          settings: []
          objects: []
          themes: []
          key_scenes: []
        """
        self.client = client if client else globals().get('client')
        self.provider = provider if provider else os.getenv("LLM_PROVIDER", "openrouter")

    def _chat(self, prompt, temperature=0.7, max_tokens=2048, model=None):
        # Use BookFactory's model selection if available
        model = model or (BookFactory().select_model("complex") if 'BookFactory' in globals() else "gpt-4")
        response = self.client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            max_tokens=max_tokens
        )
        return response.choices[0].message.content

    def generate_initial_outline(self, user_notes: str) -> Dict:
        prompt = f"""Transform these story notes into a structured outline using this format:
        {self.outline_template}
        
        Notes:
        {user_notes}
        
        Create:
        1. Narrative structure with 3 chapters per part
        2. Detailed story elements section
        3. Key scenes that should appear in specific chapters
        """
        return yaml.safe_load(self._chat(prompt, temperature=0.7, max_tokens=2048))

    def expand_element(self, element_type: str, current_data: Dict) -> Dict:
        prompt = f"""Enrich this {element_type} description. Add:
        - Physical characteristics
        - Motivations
        - Relationships to other elements
        - Symbolic significance
        - 3 unique traits
        
        Current {element_type}:
        {yaml.dump(current_data)}
        """
        return yaml.safe_load(self._chat(prompt, temperature=0.6, max_tokens=1024))

    def generate_chapter_content(self, outline: Dict, chapter_num: int) -> str:
        chapter = outline['narrative_structure'][chapter_num]
        elements = outline['story_elements']
        prompt = f"""Write a 2000-word chapter titled \"{chapter['title']}\" containing:
        - Key scenes: {', '.join(chapter.get('key_scenes', []))}
        - Characters: {', '.join([c['name'] for c in elements['characters'] if c['name'] in chapter['characters']])}
        - Setting: {chapter['setting']}
        
        Incorporate these themes: {', '.join(elements['themes'])}
        Maintain this tone: {outline.get('metadata', {}).get('tone', 'default')}
        
        Structure:
        1. Opening hook related to {elements['key_scenes'][0]['name'] if elements['key_scenes'] else 'the main conflict'}
        2. Develop character relationships between {chapter.get('character_relationships', 'main characters')}
        3. Build toward {chapter.get('next_chapter_hook', 'the next major event')}
        """
        return self._chat(prompt, temperature=0.8, max_tokens=3000)

    def validate_coherence(self, outline: Dict, new_content: str) -> bool:
        prompt = f"""Verify consistency between new content and story bible:
        Story Bible:
        {yaml.dump(outline['story_elements'])}
        
        New Content:
        {new_content}
        
        Identify any contradictions in:
        - Character motivations
        - Setting details
        - Magic system rules
        - Timeline consistency
        
        Respond ONLY with 'Valid' or list of issues (max 3)"""
        result = self._chat(prompt, temperature=0.3, max_tokens=512)
        return "Valid" in result