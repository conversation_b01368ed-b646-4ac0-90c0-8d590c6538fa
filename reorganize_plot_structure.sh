#!/bin/bash

# Script to reorganize plot structure for better LLM processing
# Run from the project root directory

# Create new directory structure
mkdir -p series_design/series_plot
mkdir -p books

# Move books to new structure
for book in 01_Book-Climax_of_Creation 02_Book-The_Golden_Thread 03_Book-Seeds_of_Athaelgard; do
  mkdir -p books/$book/plot/scenes
  mkdir -p books/$book/chapters
  
  # Move chapters to new location
  if [ -d "$book/chapters" ]; then
    cp -r $book/chapters/* books/$book/chapters/
  fi
  
  # Create plot structure
  touch books/$book/plot/outline.md
  touch books/$book/plot/character_arcs.md
  touch books/$book/plot/timeline.md
  
  # Create acts directories for scenes
  mkdir -p books/$book/plot/scenes/act1
  mkdir -p books/$book/plot/scenes/act2
  mkdir -p books/$book/plot/scenes/act3
done

# Create series plot documents
touch series_design/series_plot/series_arc.md
touch series_design/series_plot/book_connections.md

# Consolidate plot information
if [ -f "plot/plot_book1.md" ]; then
  cp plot/plot_book1.md books/01_Book-Climax_of_Creation/plot/outline.md
fi

if [ -f "plot/book1_timeline.md" ]; then
  cp plot/book1_timeline.md books/01_Book-Climax_of_Creation/plot/timeline.md
fi

if [ -f "plot/plot_book2.md" ]; then
  cp plot/plot_book2.md books/02_Book-The_Golden_Thread/plot/outline.md
fi

if [ -f "plot/plot_book3.md" ]; then
  cp plot/plot_book3.md books/03_Book-Seeds_of_Athaelgard/plot/outline.md
fi

# Consolidate character arcs
for book_num in 01 02 03; do
  book_dir=$(ls -d *${book_num}_Book* | head -1)
  if [ -z "$book_dir" ]; then
    continue
  fi
  
  # Extract book number for file naming
  book_num_only=$(echo $book_dir | cut -d'-' -f1 | sed 's/_Book//')
  
  # Create character arcs file with index
  echo "# Character Arcs for Book $book_num_only" > books/$book_dir/plot/character_arcs.md
  echo "" >> books/$book_dir/plot/character_arcs.md
  echo "## Index" >> books/$book_dir/plot/character_arcs.md
  
  # Find all character files and add them to the index
  if [ -d "plot/plot_character_arcs" ]; then
    for char_file in plot/plot_character_arcs/*.md; do
      if [ "$char_file" != "plot/plot_character_arcs/index.md" ]; then
        char_name=$(basename $char_file .md)
        capitalized_char_name=$(echo "$char_name" | awk '{printf "%s%s", toupper(substr($0,1,1)), substr($0,2)}')
        echo "- [${capitalized_char_name}](#${char_name})" >> books/$book_dir/plot/character_arcs.md
      fi
    done
    # Add content from each character file
    echo "" >> books/$book_dir/plot/character_arcs.md
    for char_file in plot/plot_character_arcs/*.md; do
      if [ "$char_file" != "plot/plot_character_arcs/index.md" ]; then
        cat $char_file >> books/$book_dir/plot/character_arcs.md
        echo "" >> books/$book_dir/plot/character_arcs.md
        echo "---" >> books/$book_dir/plot/character_arcs.md
        echo "" >> books/$book_dir/plot/character_arcs.md
      fi
    done
  fi
done

# Create plot index document for each book
for book in books/*/; do
  book=${book%/}
  cat > $book/plot/index.md << EOF
# Plot Index

## Structure
- [Outline](outline.md) - Complete plot structure
- [Timeline](timeline.md) - Chronological events
- [Character Arcs](character_arcs.md) - Character development

## Scenes by Act
- [Act 1](scenes/act1/) - Setup and introduction
- [Act 2](scenes/act2/) - Rising action and complications
- [Act 3](scenes/act3/) - Climax and resolution
EOF
done

# Create series plot index
cat > series_design/series_plot/index.md << EOF
# Series Plot Index

- [Series Arc](series_arc.md) - Overall series arc and themes
- [Book Connections](book_connections.md) - How books connect to each other

## Books
- [Book 1: Climax of Creation](../../books/01_Book-Climax_of_Creation/plot/index.md)
- [Book 2: The Golden Thread](../../books/02_Book-The_Golden_Thread/plot/index.md)
- [Book 3: Seeds of Athaelgard](../../books/03_Book-Seeds_of_Athaelgard/plot/index.md)
EOF

echo "Plot structure reorganization complete. New structure is in books/ and series_design/series_plot/"
echo "Original files are preserved. Review the new structure and delete original files when satisfied."