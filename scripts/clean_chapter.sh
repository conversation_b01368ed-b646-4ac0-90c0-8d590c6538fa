#!/bin/zsh

# Exit on error
set -e

# Determine the directory where the script resides
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# Check if a filename was provided
if [ -z "$1" ]; then
  echo "Usage: $0 <filename>"
  exit 1
fi

FILENAME="$1"
TMP_FILE=$(mktemp)

# Ensure cleanup on exit
trap 'rm -f "$TMP_FILE"' EXIT

# Check if the file exists
if [ ! -f "$FILENAME" ]; then
  echo "Error: File '$FILENAME' not found."
  exit 1
fi

echo "Cleaning chapter: $FILENAME"

# 1. Fix spacing using the Python script
# Check if uv is available, otherwise use python directly
if command -v uv &> /dev/null; then
    uv run "$SCRIPT_DIR/fix_spacing.py" "$FILENAME" "$FILENAME"
else
    python3 "$SCRIPT_DIR/fix_spacing.py" "$FILENAME" "$FILENAME"
fi

# 2. Remove numbered prefix, chapter title prefix.
cat "$FILENAME" | \
sed 's/^[0-9]* | //g' | \
sed 's/^# Chapter.*: /# /g' > "$TMP_FILE"

# 3. Replace the original file with the cleaned version
mv "$TMP_FILE" "$FILENAME"

echo "Cleaned '$FILENAME' successfully."
exit 0
