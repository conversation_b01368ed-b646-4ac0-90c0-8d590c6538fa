import sys
import re

def fix_spacing(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_lines = []
    last_non_blank_line = None
    # Start true to prevent leading blank lines unless the first line is intentionally blank
    previous_was_blank = True 

    for line in lines:
        # Remove trailing whitespace and normalize internal spaces/tabs
        stripped = re.sub(r'[ \t]+', ' ', line.strip())

        if stripped:
            # Check against the last recorded non-blank line for deduplication
            if stripped != last_non_blank_line:
                processed_lines.append(stripped)
                last_non_blank_line = stripped
                previous_was_blank = False
            # else: Skip this line as it's a consecutive duplicate of the previous non-blank line
        else:
            # Handle blank lines: collapse multiple consecutive blanks into one
            if not previous_was_blank:
                processed_lines.append('') # Add a single blank line representation
                previous_was_blank = True
            # Reset last_non_blank_line when a blank line is encountered.
            # This ensures paragraphs separated by blank lines aren't deduplicated.
            last_non_blank_line = None 

    # Remove any trailing blank lines from the end of the processed list
    while processed_lines and processed_lines[-1] == '':
        processed_lines.pop()

    # Join the processed lines back with newline characters
    final_content = '\n'.join(processed_lines)

    # Ensure the file ends with exactly one newline character (no trailing blank lines yet)
    if final_content:
        final_content += '\n' # Changed from '\n\n'

    # Write the fixed content to the output file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(final_content)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python fix_spacing.py input.md output.md")
    else:
        fix_spacing(sys.argv[1], sys.argv[2])