import os
import subprocess
import sys

def convert_md_to_docx(md_file, docx_file):
    """Converts a Markdown file to DOCX using pandoc."""
    try:
        # Check if pandoc is installed
        subprocess.run(["pandoc", "--version"], check=False, capture_output=True)
    except FileNotFoundError:
        print("Error: pandoc is not installed. Please install it using your system's package manager.")
        print("For example, on Debian/Ubuntu: sudo apt-get install pandoc")
        print("On macOS: brew install pandoc")
        sys.exit(1)

    if not os.path.exists(md_file):
        print(f"Error: Markdown file '{md_file}' not found.")
        sys.exit(1)

    try:
        subprocess.run(["pandoc", md_file, "-o", docx_file], check=True)
        print(f"Successfully converted '{md_file}' to '{docx_file}'")
    except subprocess.CalledProcessError as e:
        print(f"Error: pandoc conversion failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python md_to_docx.py <input.md> <output.docx>")
        sys.exit(1)

    md_file = sys.argv[1]
    docx_file = sys.argv[2]
    convert_md_to_docx(md_file, docx_file)