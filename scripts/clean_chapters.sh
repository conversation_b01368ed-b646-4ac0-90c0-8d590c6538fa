#!/bin/zsh

# Exit on error
set -e

# Determine the directory where the script resides
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# if $1 does not contain "chapters", then append it
if [[ "$1" != *"chapters"* ]]; then
    echo "Appending 'chapters' to the directory path."
    CHAPTER_DIR="$1/chapters"
else
    CHAPTER_DIR="$1"
fi

if [ ! -d "$CHAPTER_DIR" ]; then
    echo "Error: Directory '$CHAPTER_DIR' does not exist."
    exit 1
fi

find "$CHAPTER_DIR" -name '*.md' | sort -V | while read -r file; do
    bash $SCRIPT_DIR/clean_chapter.sh "$file"
done
