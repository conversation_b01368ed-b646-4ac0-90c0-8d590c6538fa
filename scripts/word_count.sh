#!/bin/zsh

# Exit on error
set -e

# Determine the directory where the script resides
SCRIPT_DIR=$(dirname "$(realpath "$0")")
# Assume the project root is the parent directory of the script's directory
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")

# if $1 does not contain "chapters", then append it
if [[ "$1" != *"chapters"* ]]; then
    CHAPTER_DIR="$1/chapters"
else
    CHAPTER_DIR="$1"
fi

if [ ! -d "$CHAPTER_DIR" ]; then
    echo "Error: Directory '$CHAPTER_DIR' does not exist."
    exit 1
fi

# Check if uv is available, otherwise use python directly
if command -v uv &> /dev/null; then
    find "$CHAPTER_DIR" -name '*.md' | sort -V | uv run $SCRIPT_DIR/count_manuscript.py
else
    find "$CHAPTER_DIR" -name '*.md' | sort -V | python3 $SCRIPT_DIR/count_manuscript.py
fi
