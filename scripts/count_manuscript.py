#!/usr/bin/env python3
import os
import math
import re
import sys # Import sys

def count_words_in_file(filepath):
    """Reads a file and counts the words."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            # Basic word count by splitting whitespace
            words = content.split()
            return len(words)
    except Exception as e:
        print(f"Error reading file {filepath}: {e}")
        return 0

def main():
    """Main function to count words and estimate pages."""
    # Read file paths from standard input
    manuscript_files = [line.strip() for line in sys.stdin if line.strip()]

    if not manuscript_files:
        print("Error: No manuscript file paths provided via stdin.", file=sys.stderr)
        sys.exit(1)

    total_word_count = 0
    words_per_page = 300  # Standard manuscript estimate
    file_stats = []

    # Remove the old file searching logic (glob, os.listdir, etc.)

    # Sort the files based on the numeric prefix in the basename
    try:
        # Extract number from basename like '010_the_cage.md' -> 10
        manuscript_files.sort(key=lambda x: int(re.match(r'(\d+)', os.path.basename(x)).group(1)) if re.match(r'(\d+)', os.path.basename(x)) else float('inf'))
    except Exception as e:
        print(f"Warning: Could not sort files numerically, using default sort. Error: {e}", file=sys.stderr)
        manuscript_files.sort() # Fallback sort

    print("--- Individual File Stats ---")
    for filepath in manuscript_files:
        # Basic check if the path exists and is a file
        if not os.path.isfile(filepath):
            print(f"- Skipping non-file: {filepath}")
            continue

        word_count = count_words_in_file(filepath)
        # Always print a line for each file found, even if empty/error
        estimated_pages = math.ceil(word_count / words_per_page) if word_count > 0 else 0

        # Extract chapter title from filename (new structure)
        basename = os.path.basename(filepath)
        match = re.match(r'([0-9]+)_(.*)\.md$', basename)
        if match:
            chapter_num_str = match.group(1)
            # Capitalize words after underscores/spaces, handle hyphens
            chapter_name = ' '.join(word.capitalize() for word in match.group(2).replace('_', ' ').replace('-', ' - ').split())
            chapter_title = f"Chapter {chapter_num_str}: {chapter_name}"
        else: # Fallback for unexpected names
             chapter_title = basename.replace('.md', '')

        file_stats.append({
            "title": chapter_title,
            "words": word_count,
            "pages": estimated_pages
        })
        print(f"Words: {word_count:<5} Pages: {estimated_pages:<3} {chapter_title:<55}")
        total_word_count += word_count
        # Error message for reading is handled within count_words_in_file

    if file_stats: # Check if any files were processed
        total_estimated_page_count = math.ceil(total_word_count / words_per_page)
        print("\n--- Manuscript Totals ---")
        print(f"Total Word Count: {total_word_count}")
        print(f"Total Estimated Page Count (@{words_per_page} words/page): {total_estimated_page_count}")
    else:
        print("\nNo valid manuscript files processed.")

if __name__ == "__main__":
    main()