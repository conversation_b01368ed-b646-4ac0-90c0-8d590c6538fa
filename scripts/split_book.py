#!/usr/bin/env python3

import os
import re
import argparse

def write_chapter(filename, lines):
    # Remove trailing blank lines
    while lines and lines[-1].strip() == "":
        lines.pop()
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    with open(filename, "w", encoding="utf-8") as out:
        if not lines:
            out.write("\n")
            return
        # Write all lines except last
        for line in lines[:-1]:
            out.write(line)
        # Write last line, ensure it ends with exactly one newline
        out.write(lines[-1].rstrip('\n') + "\n")

def split_book(input_file="book.md", output_dir=".", output_prefix="chapter_"):
    if not os.path.isfile(input_file):
        raise IsADirectoryError(f"The input_file '{input_file}' is a directory. Please provide a valid file.")

    with open(input_file, "r", encoding="utf-8") as f:
        lines = f.readlines()

    chapter_count = 0
    chapter_lines = []
    for line in lines:
        prologue_match = re.match(r'^# Prologue', line)
        chapter_match = re.match(r'^# Chapter', line)
        if prologue_match or chapter_match:
            if chapter_lines:
                filename = os.path.join(output_dir, f"{output_prefix}{chapter_count:02d}.md")
                write_chapter(filename, chapter_lines)
                print(f"Wrote {filename}")
                chapter_lines = []

            if prologue_match:
                chapter_count = 0
            else:
                chapter_count += 1
        chapter_lines.append(line)

    # Write the last chapter if any
    if chapter_lines:
        filename = os.path.join(output_dir, f"{output_prefix}{chapter_count:02d}.md")
        write_chapter(filename, chapter_lines)
        print(f"Wrote {filename}")

def main():
    parser = argparse.ArgumentParser(description="Split a book.md file into chapter files in a specified directory.")
    parser.add_argument("input_file", nargs="?", default="book.md", help="Input book file (default: book.md)")
    parser.add_argument("output_dir", nargs="?", default=".", help="Output directory for chapters (default: current directory)")
    parser.add_argument("--prefix", default="chapter_", help="Prefix for output chapter files (default: chapter_)")
    args = parser.parse_args()

    split_book(input_file=args.input_file, output_dir=args.output_dir, output_prefix=args.prefix)

if __name__ == "__main__":
    main()