import os
import re
import sys
import glob

PROJECT_ROOT = "."
# Updated pattern to capture number and optional letter suffix
CHAPTER_PATTERN = re.compile(r"^(\d+)([a-z]?)_.*\.md$")
TITLE_LINE_PATTERN = re.compile(r"^# Chapter\s*(.*?)$", re.IGNORECASE)

def sanitize_title(title):
    return (
        title.lower()
        .replace("'", "")
        .replace('"', "")
        .replace("'", "")
        .replace("'", "")
        .replace("–", "-")
        .replace("—", "-")
        .replace(" ", "_")
        .replace(":", "")
        .replace("?", "")
        .replace(",", "")
        .replace(".", "")
        .replace("!", "")
        .replace("(", "")
        .replace(")", "")
        .replace("[", "")
        .replace("]", "")
        .replace("/", "_")
        .replace("\\", "_")
    )

def get_chapter_files(book_dir):
    # Check for the new directory structure first
    chapters_dir = os.path.join(book_dir, "chapters")
    if os.path.isdir(chapters_dir):
        # Get all files from numbered subdirectories
        chapter_files = []
        for chapter_num_dir in os.listdir(chapters_dir):
            chapter_dir_path = os.path.join(chapters_dir, chapter_num_dir)
            if os.path.isdir(chapter_dir_path) and chapter_num_dir.isdigit():
                # Get all markdown files in this chapter directory
                md_files = [f for f in os.listdir(chapter_dir_path) if f.endswith(".md")]
                for md_file in md_files:
                    chapter_files.append((int(chapter_num_dir), os.path.join(chapter_dir_path, md_file)))
        # Sort by chapter number
        chapter_files = sorted(chapter_files, key=lambda x: x[0])
        return [file_path for _, file_path in chapter_files]
    else:
        # Fall back to old pattern
        # Filter files based on the updated pattern
        chapter_files = [f for f in os.listdir(book_dir) if f.endswith(".md") and CHAPTER_PATTERN.match(f)]
        
        # Sort files: numerically by chapter number, then alphabetically by suffix
        def sort_key(filename):
            match = CHAPTER_PATTERN.match(filename)
            if match:
                num_part = int(match.group(1))
                suffix_part = match.group(2) # Will be '' if no suffix
                return (num_part, suffix_part)
            return (0, '') # Default for safety, though should not happen with filtering

        files = sorted(chapter_files, key=sort_key)
        return [os.path.join(book_dir, f) for f in files]

def clean_chapter_title(title):
    # Repeatedly remove leading chapter identifiers like "01: ", "24a: ", " 24a : " etc.
    pattern = re.compile(r"^\s*\d+[a-z]?\s*:\s*")
    original_title = title # Keep for logging if needed
    cleaned = title.strip()
    
    # Loop to remove potentially multiple prefixes like "01: 01: Title"
    while True:
        # Apply pattern and strip immediately
        new_cleaned = pattern.sub("", cleaned, count=1).strip() 
        if new_cleaned == cleaned: # No change means no more matches
            break
        cleaned = new_cleaned

    # If after removing all valid prefixes with colons, we are left with something that
    # still looks like a chapter ID (e.g., "24a The Hidden Grove" because the colon was missing),
    # try removing that pattern too.
    pattern2 = re.compile(r"^\d+[a-z]?\s+")
    match2 = pattern2.match(cleaned)
    if match2:
         # Check if removing this would leave an empty string.
         temp_cleaned = pattern2.sub("", cleaned, count=1).strip() # Strip after sub
         if temp_cleaned: # Only apply if it doesn't empty the title
             cleaned = temp_cleaned
         # else: # Optional: Log or handle cases where removing empties the title
         #     print(f"Warning: Removing leading pattern '{match2.group(0).strip()}' from '{cleaned}' would empty the title. Keeping fragment.")

    # Final cleanup of multiple spaces and strip again
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    # If cleaning resulted in an empty string, log a warning.
    if not cleaned:
        print(f"Warning: Cleaning title '{original_title}' resulted in empty string.")
        # Returning empty string, caller must handle.

    return cleaned # Return potentially empty string, handled in rename_and_update_chapters

def rename_and_update_chapters(book_dir):
    files = get_chapter_files(book_dir)
    # Remove the pre-check; title validation will happen inside the loop
    # missing, titles_map = check_chapter_titles(book_dir, files) 
    
    # if missing:
    #     print("Aborting: The following files are missing a valid chapter title line or could not be read:")
    #     for f in missing:
    #         print(f"  {f}")
    #     sys.exit(1)
    
    mapping = {}
    processed_files = set() # Keep track of files already processed to avoid double processing after rename
    files_with_errors = [] # Keep track of files that cause issues during processing
    
    # Initialize counter for sequential chapter numbering starting at 1
    chapter_counter = 0

    # Check if we're using the new directory structure
    chapters_dir = os.path.join(book_dir, "chapters")
    using_new_structure = os.path.isdir(chapters_dir)

    # Use the sorted list of files directly
    for filepath in files:
        if filepath in processed_files:
            continue

        old_path = filepath
        filename = os.path.basename(filepath)
        dir_path = os.path.dirname(filepath)
        
        # --- Read current title from file ---
        current_title = ""
        original_first_line = ""
        lines = []
        try:
            with open(old_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            if not lines:
                 print(f"Warning: File {filename} is empty. Skipping.")
                 files_with_errors.append(filename)
                 continue
            
            original_first_line = lines[0].strip()
            m = TITLE_LINE_PATTERN.match(original_first_line)
            if not m or not m.group(1):
                print(f"Error: File {filename} is missing a valid chapter title line: '{original_first_line}'. Skipping.")
                files_with_errors.append(filename)
                continue
            
            # Get the title part currently in the file
            current_title = m.group(1) 
            
        except FileNotFoundError:
             print(f"Warning: File {old_path} not found during processing loop. Skipping.")
             # This might happen if a previous rename failed unexpectedly, or file deleted externally
             files_with_errors.append(filename)
             continue 
        except Exception as e:
             print(f"Error reading file {old_path} for title extraction: {e}")
             files_with_errors.append(filename)
             continue
        # --- End Read current title ---

        # Clean the title extracted from the file
        cleaned_current_title = clean_chapter_title(current_title)
        if not cleaned_current_title:
             print(f"Warning: Cleaned title for {filename} ('{current_title}') is empty. Using placeholder 'untitled_chapter'.")
             cleaned_current_title = "untitled_chapter" # Use placeholder for filename generation

        # Increment the counter for every file processed
        chapter_counter += 1
        
        # Construct the chapter ID using only the incremented counter (no suffix)
        chapter_id = f"{chapter_counter:02d}"

        # Sanitize the cleaned title for the filename
        safe_title = sanitize_title(cleaned_current_title) 
        # Handle empty safe_title (redundant check, but safe)
        if not safe_title:
            safe_title = "untitled_chapter"

        # For new directory structure, we only need the title (no chapter number in filename)
        if using_new_structure:
            new_filename_base = safe_title
        else:
            # For old structure, include chapter number in filename
            new_filename_base = f"{chapter_id}_{safe_title}"

        # --- Robust cleaning of the generated base filename ---
        while "__" in new_filename_base:
            new_filename_base = new_filename_base.replace('__', '_')
        if new_filename_base.startswith('_'):
            new_filename_base = new_filename_base[1:]
        if new_filename_base.endswith('_'):
            new_filename_base = new_filename_base[:-1]
        
        if using_new_structure:
            # For new structure, the title might become empty after cleaning
            if not new_filename_base:
                new_filename_base = "untitled_chapter"
        else:
            # For old structure, verify we still have title part after cleaning
            parts = new_filename_base.split('_', 1)
            if len(parts) < 2 or not parts[1]:
                 print(f"Warning: Filename base for {filename} became empty or lost title part after cleaning ('{new_filename_base}'). Appending placeholder.")
                 new_filename_base = f"{chapter_id}_untitled_chapter" 
                 while "__" in new_filename_base: new_filename_base = new_filename_base.replace('__', '_')
                 if new_filename_base.endswith('_'): new_filename_base = new_filename_base[:-1]

        new_filename = f"{new_filename_base}.md"
        
        # For new structure, ensure the chapter directory exists
        if using_new_structure:
            # Determine which chapter directory this file belongs to
            if os.path.basename(dir_path).isdigit():  # File already in a chapter dir
                chapter_num_dir = os.path.basename(dir_path)
            else:
                # If file not in a chapter directory, use the chapter_counter to determine where it should go
                chapter_num_dir = str(chapter_counter)
            
            # Create full path to the target directory
            target_dir = os.path.join(chapters_dir, chapter_num_dir)
            os.makedirs(target_dir, exist_ok=True)
            
            new_path = os.path.join(target_dir, new_filename)
        else:
            # For old structure, keep the file in the same directory
            new_path = os.path.join(dir_path, new_filename)

        # --- Update title line in file content ---
        # Use the determined purely numeric chapter_id and the cleaned_current_title
        new_title_line = f"# Chapter {chapter_id}: {cleaned_current_title}\n" 
        title_updated = False
        if lines[0] != new_title_line:
            lines[0] = new_title_line
            try:
                with open(old_path, "w", encoding="utf-8") as f2:
                    f2.writelines(lines)
                print(f"Updated title in {filename}") 
                title_updated = True
            except Exception as e:
                 print(f"Error writing updated title to {old_path}: {e}")
                 # Continue processing? Or mark as error? Let's mark and continue.
                 files_with_errors.append(filename)
                 # If title write failed, don't proceed with rename for this file? Safer not to.
                 continue 
        # --- End Update title line ---

        # Rename file if needed
        if old_path != new_path:
            if os.path.exists(new_path):
                 print(f"Warning: Target file {new_path} already exists. Skipping rename for {filename}.")
                 new_path = old_path 
                 new_filename = filename 
            else:
                try:
                    os.rename(old_path, new_path)
                    print(f"Renamed {filename} -> {new_filename}")
                    processed_files.add(new_path) 
                except OSError as e:
                    print(f"Error renaming {filename} to {new_filename}: {e}")
                    # If rename fails, the original file still exists (potentially with updated title)
                    new_path = old_path 
                    new_filename = filename 
                    processed_files.add(filepath) # Mark original as processed (or errored?)
                    files_with_errors.append(filename) # Mark as error
        else:
            # No rename needed
            new_path = old_path
            new_filename = filename
            processed_files.add(filepath) 

        # Mapping - Map original filepath to final filepath (even if same or rename failed)
        mapping[filepath] = new_path
        # Map original first line to the new title line (stripped)
        if original_first_line:
             mapping[original_first_line] = new_title_line.strip() 

    # Report any files skipped due to errors
    if files_with_errors:
        print("\nWarning: The following files encountered errors during processing and might need manual review:")
        for f_err in set(files_with_errors): # Use set to avoid duplicates
            print(f"  {f_err}")

    return mapping

def update_references_in_md_files(mapping):
    # Search all .md files in project (recursively)
    for root, dirs, files in os.walk(PROJECT_ROOT):
        for file in files:
            if file.endswith(".md"):
                path = os.path.join(root, file)
                with open(path, "r", encoding="utf-8") as f:
                    content = f.read()
                new_content = content
                for old, new in mapping.items():
                    new_content = re.sub(re.escape(old), new, new_content)
                if new_content != content:
                    with open(path, "w", encoding="utf-8") as f:
                        f.write(new_content)

def main():
    if len(sys.argv) != 2 or sys.argv[1] in ("-h", "--help"):
        print("Usage: python rename_chapters.py <Book-Directory>")
        print("Example: python rename_chapters.py Book-1")
        sys.exit(1)
    book_dir = sys.argv[1]
    if not os.path.isdir(book_dir):
        print(f"Error: Directory '{book_dir}' does not exist.")
        sys.exit(1)
    mapping = rename_and_update_chapters(book_dir)
    update_references_in_md_files(mapping)
    print(f"Renaming, title update, and reference update complete for {book_dir}.")

if __name__ == "__main__":
    main()