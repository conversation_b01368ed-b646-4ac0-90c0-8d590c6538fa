#!/bin/zsh

# Exit on error
set -e

# Determine the directory where the script resides
SCRIPT_DIR=$(dirname "$(realpath "$0")")
# Assume the project root is the parent directory of the script's directory
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")

# if $1 does not contain "chapters", then append it
if [[ "$1" != *"chapters"* ]]; then
    CHAPTER_DIR="$1/chapters"
else
    CHAPTER_DIR="$1"
fi

if [ ! -d "$CHAPTER_DIR" ]; then
    echo "Error: Directory '$CHAPTER_DIR' does not exist."
    exit 1
fi

BOOK_DIR=$(dirname "$CHAPTER_DIR")
if [ -z "$2" ]; then
    # Extract the book name from the directory path
    BOOK_NAME=$(basename "$BOOK_DIR" | sed 's/^[0-9]*_Book-//')
    BOOK_PATHNAME="${PROJECT_ROOT}/${BOOK_NAME}.md"
else
    BOOK_PATHNAME="$2"
fi

if [ -f "$BOOK_PATHNAME" ]; then
    rm "$BOOK_PATHNAME"
fi

CHAPTER_NUM=0
find "$CHAPTER_DIR" -name '*.md' | sort -V | while read -r file; do
    echo "Adding chapter: $file"
    CHAPTER_NUM=$((CHAPTER_NUM + 1))
    # Check if uv is available, otherwise use python directly
    if command -v uv &> /dev/null; then
        uv run $SCRIPT_DIR/fix_spacing.py "$file" "$file"
    else
        python3 $SCRIPT_DIR/fix_spacing.py "$file" "$file"
    fi
    cat "$file" | sed 's/^[0-9]* | //g' | \
    sed 's/^# /# Chapter '"$CHAPTER_NUM: "'/g' >> "$BOOK_PATHNAME"
    echo "" >> "$BOOK_PATHNAME"
done

# Remove the final empty line, on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' '$d' "$BOOK_PATHNAME"
else
    sed -i '$d' "$BOOK_PATHNAME"
fi

echo "Book '$BOOK_TITLE' created successfully at '$BOOK_PATHNAME'."