#!/usr/bin/env python3

import os
import re
import html
import argparse
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Flowable
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from PIL import Image as PILImage # Used for image dimensions
import subprocess # Keep subprocess for page count attempt

# --- Constants ---
# Define page size globally - used by callbacks and layout
page_width, page_height = letter

# --- Global Variables (Minimize Use) ---
# These are primarily needed for ReportLab callbacks which have limited parameters.
chapter_start_pages = {} # Stores {chapter_title: physical_page_number} from Pass 1
first_chapter_title_pass1 = None # Stores the title of the first chapter found in Pass 1
# Default physical page number where content starts in the final PDF (after cover, blank page, title, TOC)
first_content_page_num_physical = 6
doc_pass1 = None # Holds the Pass 1 document object for the afterFlowable callback
main_font = 'Times-Roman' # Default fallback font name

# --- Helper Functions ---

def clean_markdown(text):
    """Convert markdown formatting to ReportLab compatible HTML tags."""
    # 1. Process bold markdown first using non-greedy matching
    text = re.sub(r'\*\*(.+?)\*\*', r'<b>\1</b>', text)
    text = re.sub(r'__(.+?)__', r'<b>\1</b>', text)

    # 2. Process italic markdown using non-greedy matching
    #    This relies on bold being processed first.
    text = re.sub(r'\*(.+?)\*', r'<i>\1</i>', text)
    text = re.sub(r'_(.+?)_', r'<i>\1</i>', text)

    # 3. Return text with HTML tags; ReportLab's Paragraph(xmlLike=True) will parse them.
    return text

# --- PDF Generation Flowables and Callbacks ---

class ChapterMarker(Flowable):
    """A flowable that marks the position of a chapter title for page number recording."""
    def __init__(self, title):
        Flowable.__init__(self)
        self.title = title
        self._marked = False # Flag to ensure it's processed only once per build

    def draw(self):
        # This flowable doesn't draw anything visible
        pass

    def wrap(self, availWidth, availHeight):
        # Consumes no space
        return (0, 0)

def after_flowable_pass1(flowable):
    """Callback used in Pass 1 to record the physical page number of ChapterMarker flowables."""
    global chapter_start_pages, first_chapter_title_pass1, doc_pass1
    if isinstance(flowable, ChapterMarker) and hasattr(flowable, 'title') and not flowable._marked:
        current_page = doc_pass1.page
        title = flowable.title
        chapter_start_pages[title] = current_page
        flowable._marked = True # Mark as processed for this build pass
        # Record the first chapter title encountered
        if first_chapter_title_pass1 is None:
            first_chapter_title_pass1 = title
        # print(f"Pass 1: Recorded '{title}' on page {current_page}") # Debug print

def add_page_number(canvas, doc):
    """Callback to add page numbers and header to each page (except the first few)."""
    global main_font, first_content_page_num_physical
    canvas.saveState()
    canvas.setFont(main_font, 10)
    page_num = canvas.getPageNumber()

    # Don't show page number/header until the first actual content page
    if page_num >= first_content_page_num_physical:
        # Add decorative elements
        canvas.setStrokeColor(colors.lightgrey)
        # Corrected line coordinates (x1, y1, x2, y2)
        canvas.line(1*inch, 0.7*inch, page_width-1*inch, 0.7*inch)

        # Add page number - Adjust to start content numbering at 1
        # Calculate based on the actual first content page
        content_page_num = page_num - first_content_page_num_physical + 1
        canvas.setFillColor(colors.darkgrey)
        canvas.drawCentredString(page_width/2, 0.5*inch, str(content_page_num))

        # Add book title to header
        canvas.setFont(main_font, 9)
        canvas.drawString(1*inch, page_height-0.5*inch, "The Obsidian Spire Queendom")

    canvas.restoreState()

def first_page(canvas, doc):
    """Callback to add the cover image to the first page."""
    canvas.saveState()
    try:
        # Get image dimensions
        img = PILImage.open("Front.jpg")
        img_width, img_height = img.size

        # Calculate aspect ratio
        aspect = img_height / float(img_width)

        # Set image width to fit page width with small margins
        image_width = page_width - 1*inch  # 0.5 inch margins on each side
        image_height = image_width * aspect

        # If image is too tall, scale it to fit the page height
        if image_height > page_height - 1*inch:
            image_height = page_height - 1*inch
            image_width = image_height / aspect

        # Center the image on the page
        x = (page_width - image_width) / 2
        y = (page_height - image_height) / 2

        # Draw the image
        canvas.drawImage("Front.jpg", x, y, width=image_width, height=image_height)
        print(f"Added cover image: {image_width} x {image_height}")
    except FileNotFoundError:
        print("Warning: Cover image 'Front.jpg' not found. Skipping cover page.")
    except Exception as e:
        print(f"Error adding cover image: {e}")
    canvas.restoreState()

# --- Font and Style Setup ---

def register_fonts():
    """Registers fonts and returns the name of the main font to use."""
    fallback_font_name = 'Times-Roman' # ReportLab's built-in (maps to Times variant, should have italic/bold)
    font_to_use = fallback_font_name # Force use of the built-in font

    print(f"--- Using ReportLab built-in font: {font_to_use} ---")
    # --- Removed all TTF registration logic ---
    # No need to search for paths or call pdfmetrics.registerFont

    return font_to_use

def get_book_styles(font_name):
    """Returns a dictionary of ParagraphStyle objects for the book."""
    styles = getSampleStyleSheet()

    # Ensure all styles properly handle HTML/XML markup
    for style_name in styles.byName:
        # --- Ensure the correct font_name is applied to all base styles ---
        # This might be redundant if getSampleStyleSheet defaults correctly, but ensures consistency
        base_style = styles[style_name]
        base_style.fontName = font_name # Explicitly set the font for all styles
        # --- End Font Application ---
        base_style.allowWidows = 0
        base_style.allowOrphans = 0
        base_style.spaceAfter = 10
        # Ensure xmlLike is True for styles that will contain formatted text
        # Explicitly set for all styles that might receive cleaned markdown
        base_style.xmlLike = True

    # Add custom styles (ensure xmlLike=True is set and fontName is correct)
    styles.add(ParagraphStyle(name='BookTitle', fontName=font_name, fontSize=32, spaceAfter=36, alignment=TA_CENTER, leading=38, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='BookSubtitle', fontName=font_name, fontSize=18, spaceAfter=24, alignment=TA_CENTER, leading=24, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='BookAuthor', fontName=font_name, fontSize=22, spaceAfter=12, alignment=TA_CENTER, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='BookDate', fontName=font_name, fontSize=14, spaceAfter=12, alignment=TA_CENTER, textColor=colors.darkgrey, xmlLike=True))
    styles.add(ParagraphStyle(name='ChapterTitle', fontName=font_name, fontSize=22, spaceAfter=24, spaceBefore=24, alignment=TA_CENTER, leading=26, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='SectionHeading', fontName=font_name, fontSize=16, spaceAfter=12, spaceBefore=18, leading=18, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='SubsectionHeading', fontName=font_name, fontSize=14, spaceAfter=10, spaceBefore=14, leading=16, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='BookText', fontName=font_name, fontSize=12, leading=18, spaceBefore=6, spaceAfter=10, alignment=TA_JUSTIFY, firstLineIndent=24, textColor=colors.black, xmlLike=True))
    styles.add(ParagraphStyle(name='TOCTitle', fontName=font_name, fontSize=22, spaceAfter=24, spaceBefore=24, alignment=TA_CENTER, textColor=colors.darkslategray, xmlLike=True))
    styles.add(ParagraphStyle(name='TOCEntry', fontName=font_name, fontSize=12, leading=20, spaceAfter=8, textColor=colors.black, xmlLike=True)) # TOC entries might have formatted titles

    return styles

# --- Markdown Parsing and Story Building ---

def build_story_flowables(markdown_content_lines, styles, is_pass1=False):
    """
    Parses markdown content lines and builds a list of ReportLab flowables.
    Includes ChapterMarker flowables in Pass 1 for page number tracking.
    """
    story = []
    current_paragraph = ""
    first_chapter = True

    for line in markdown_content_lines:
        line = line.rstrip()
        if line.startswith('# '):
            # Add previous paragraph if exists
            if current_paragraph:
                cleaned_text = clean_markdown(current_paragraph)
                story.append(Paragraph(cleaned_text, styles['BookText']))
                current_paragraph = ""

            # Add page break before new chapter (except the very first one)
            if not first_chapter:
                story.append(PageBreak())

            title = line.lstrip('#').strip()
            story.append(Spacer(1, 0.75*inch))
            story.append(Paragraph(title, styles['ChapterTitle'])) # Add title first
            if is_pass1:
                # Add marker after title in Pass 1
                story.append(ChapterMarker(title))
            story.append(Spacer(1, 0.1*inch))
            story.append(Paragraph("* * *", styles['ChapterTitle']))
            story.append(Spacer(1, 0.3*inch))

            if first_chapter:
                first_chapter = False # Set flag after processing first chapter

        elif line.startswith('## '):
            # Add previous paragraph if exists
            if current_paragraph:
                cleaned_text = clean_markdown(current_paragraph)
                story.append(Paragraph(cleaned_text, styles['BookText']))
                current_paragraph = ""
            heading = line.lstrip('#').strip()
            story.append(Paragraph(heading, styles['SectionHeading']))
        elif line.startswith('### '):
            # Add previous paragraph if exists
            if current_paragraph:
                cleaned_text = clean_markdown(current_paragraph)
                story.append(Paragraph(cleaned_text, styles['BookText']))
                current_paragraph = ""
            heading = line.lstrip('#').strip()
            story.append(Paragraph(heading, styles['SubsectionHeading']))
        elif line.strip() == '':
            # Add previous paragraph if exists (handles multiple blank lines)
            if current_paragraph:
                cleaned_text = clean_markdown(current_paragraph)
                story.append(Paragraph(cleaned_text, styles['BookText']))
                current_paragraph = ""
        else:
            # Append line to current paragraph
            current_paragraph = (current_paragraph + " " + line) if current_paragraph else line

    # Add the last paragraph if it exists
    if current_paragraph:
        cleaned_text = clean_markdown(current_paragraph)
        story.append(Paragraph(cleaned_text, styles['BookText']))

    return story

def extract_chapter_data(input_md_path):
    """Reads the markdown file and extracts chapter titles and their sequential index."""
    chapter_data = []
    print("Extracting chapter titles...")
    try:
        with open(input_md_path, "r", encoding="utf-8") as f:
            for i, line in enumerate(f):
                stripped_line = line.strip()
                if stripped_line.startswith('# '):
                    title = stripped_line.lstrip('# ').strip()
                    # Store as (sequential_index, title)
                    chapter_data.append((len(chapter_data) + 1, title))
                    # print(f"  Found Chapter {len(chapter_data)}: {title}") # Debug print
        print(f"Found {len(chapter_data)} chapters.")
        if not chapter_data:
             print("Warning: No chapter titles found (lines starting with '# '). TOC will be empty.")
        return chapter_data
    except FileNotFoundError:
        print(f"Error: Input markdown file not found at {input_md_path}")
        return None
    except Exception as e:
        print(f"Error reading input markdown file {input_md_path} for chapter extraction: {e}")
        return None

# --- PDF Generation Passes ---

def run_pass1(input_md_path, styles):
    """
    Runs the first pass of PDF generation to calculate page numbers for chapters.
    Returns the chapter_start_pages dictionary and the physical page number
    where the first chapter content begins.
    """
    global doc_pass1, first_chapter_title_pass1, chapter_start_pages # Need globals for callback

    print("Starting Pass 1: Calculating page numbers...")
    chapter_start_pages = {} # Reset for this build
    first_chapter_title_pass1 = None # Reset for this build

    story_pass1 = [] # Initialize the story list

    # Add placeholders for front matter to simulate page numbers in the final document
    story_pass1.append(PageBreak()) # Placeholder for Cover Page (Page 1)
    # Placeholders for Title Page (Page 2)
    story_pass1.append(Spacer(1, 2*inch)) # Corresponds to title page spacing
    story_pass1.append(Spacer(1, 3*inch)) # More spacing to simulate title/author/date
    story_pass1.append(PageBreak()) # Page break after Title Page
    # Placeholders for TOC (Page 3) - Assuming TOC fits on one page for simplicity
    story_pass1.append(Spacer(1, 1*inch)) # Corresponds to TOC title spacing
    story_pass1.append(Spacer(1, 5*inch)) # Spacing to simulate TOC entries
    story_pass1.append(PageBreak()) # Page break after TOC

    try:
        with open(input_md_path, "r", encoding="utf-8") as f:
            markdown_lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: Input markdown file not found at {input_md_path} for Pass 1")
        return {}, first_content_page_num_physical # Return default on error
    except Exception as e:
        print(f"Error reading input markdown file {input_md_path} for Pass 1: {e}")
        return {}, first_content_page_num_physical # Return default on error

    story_pass1 = build_story_flowables(markdown_lines, styles, is_pass1=True)

    # Build Pass 1
    dummy_output = "dummy_pass1.pdf"
    doc_pass1 = SimpleDocTemplate(dummy_output, pagesize=letter)
    doc_pass1.afterFlowable = after_flowable_pass1 # Assign the callback

    try:
        doc_pass1.build(story_pass1)
        print(f"Pass 1 complete. Found page numbers for {len(chapter_start_pages)} chapters.")

        # The actual first content page number in the final PDF is determined by the
        # fixed front matter (cover, title, TOC). We use the initial global value.
        actual_first_content_page = first_content_page_num_physical
        print(f"Pass 1: First chapter '{first_chapter_title_pass1}' was found on physical page {chapter_start_pages.get(first_chapter_title_pass1, '?')} in the dummy document.")
        print(f"Pass 1: Page numbering in the final PDF will start from physical page {actual_first_content_page} (after front matter).")


        return chapter_start_pages, actual_first_content_page

    except Exception as e:
        print(f"Error during Pass 1 build: {e}")
        return {}, first_content_page_num_physical # Return default on error
    finally:
        # Clean up the dummy file
        if os.path.exists(dummy_output):
            os.remove(dummy_output)
        doc_pass1 = None # Clear the global doc object

def run_pass2(input_md_path, output_pdf_path, chapter_data, styles, chapter_start_pages):
    """
    Runs the second pass of PDF generation to build the final PDF with TOC and content.
    """
    print("Starting Pass 2: Building final PDF...")

    # The global first_content_page_num_physical is used by the add_page_number callback.
    # Its value is determined by the fixed front matter pages (cover, title, TOC).

    story_final = []
    story_final.append(PageBreak()) # Add a blank page after the cover

    # Add title page elements (These will appear on page 3, after cover and blank page)
    story_final.append(Spacer(1, 2*inch))
    story_final.append(Paragraph("The Obsidian Spire Queendom", styles['BookTitle']))
    story_final.append(Paragraph("Book 1", styles['BookSubtitle']))
    story_final.append(Spacer(1, 1*inch))
    story_final.append(Paragraph("Rez Khan", styles['BookAuthor']))
    story_final.append(Spacer(1, 2*inch))
    story_final.append(Paragraph("April 2025", styles['BookDate']))
    story_final.append(PageBreak()) # Page break after title page
    # print("Debug: Added title page elements and page break to story_final.") # Removed debug print

    # Add Table of Contents (Starts on page 4)
    story_final.append(Spacer(1, 1*inch))
    story_final.append(Paragraph("Table of Contents", styles['TOCTitle']))
    story_final.append(Spacer(1, 0.5*inch))

    # Calculate Page Number for TOC Entries
    # The adjusted number should represent the content page number (starting from 1)
    # based on the physical page number from Pass 1 and the start of content pages.
    print("Pass 2: Generating TOC entries...")
    for i, (_, title) in enumerate(chapter_data): # Iterate through chapter_data (sequential_index, title)
        raw_page_num = chapter_start_pages.get(title, None)
        if raw_page_num is not None:
            page_num_str = str(raw_page_num)
        else:
            page_num_str = '?' # Use '?' if page number wasn't found
            # print(f"  TOC Entry: '{title}' -> Page number not found.") # Debug print

        # Calculate dots dynamically
        # Estimate space needed for title and page number, leave space for dots
        # Adjust the multiplier (60) based on desired TOC width and font size
        dots_length = max(3, 60 - len(title) - len(page_num_str))
        dots = '.' * dots_length

        toc_entry = f"{title} {dots} {page_num_str}"
        story_final.append(Paragraph(toc_entry, styles['TOCEntry']))

    story_final.append(PageBreak()) # Page break after TOC
    # print("Debug: Added TOC elements and page break to story_final.") # Removed debug print

    # Add main content (rebuild flowables for pass 2)
    # print("Debug: Adding main content flowables to story_final.") # Removed debug print
    # Print the first few elements to inspect the order
    # print(f"Debug: First few elements of story_final before adding content: {story_final[:10]}") # Removed debug print

    try:
        with open(input_md_path, "r", encoding="utf-8") as f:
            markdown_lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: Input markdown file not found at {input_md_path} for Pass 2")
        return # Cannot proceed without content
    except Exception as e:
        print(f"Error reading input markdown file {input_md_path} for Pass 2: {e}")
        return

    story_final.extend(build_story_flowables(markdown_lines, styles, is_pass1=False))

    # Build the final PDF
    doc_final = SimpleDocTemplate(
        output_pdf_path,
        pagesize=letter,
        rightMargin=1*inch,
        leftMargin=1.25*inch,
        topMargin=1*inch,
        bottomMargin=1*inch
    )

    try:
        # The modified add_page_number will now be used correctly
        doc_final.build(story_final, onFirstPage=first_page, onLaterPages=add_page_number)
        print(f"PDF created successfully: {output_pdf_path}")
        # Print file size and page count
        if os.path.exists(output_pdf_path):
            file_size_bytes = os.path.getsize(output_pdf_path)
            file_size_kb = file_size_bytes / 1024
            file_size_mb = file_size_kb / 1024
            print(f"File size: {file_size_kb:.2f} KB ({file_size_mb:.2f} MB)")

            # Attempt to get page count using subprocess (requires a tool like pdfinfo or pdftk)
            # This part might fail if the tool isn't installed, but it's useful if available.
            try:
                # Example using pdfinfo (part of poppler-utils on Linux, or via brew on macOS)
                # On Windows, you might need a different tool or approach.
                if os.name == 'posix': # Linux or macOS
                    result = subprocess.run(['pdfinfo', output_pdf_path], capture_output=True, text=True)
                    if result.returncode == 0:
                        for line in result.stdout.splitlines():
                            if 'Pages:' in line:
                                page_count = int(line.split(':')[1].strip())
                                print(f"Page count: {page_count}")
                                break
                    else:
                        print(f"Warning: Could not get page count using pdfinfo. Error: {result.stderr.strip()}")
        
            except FileNotFoundError:
                print("Warning: pdfinfo or pdftk not found. Cannot determine page count.")
            except Exception as e:
                print(f"Warning: Error getting page count: {e}")

    except Exception as e:
        print(f"Error during Pass 2 build: {e}")
        # Consider raising e here if you want the script to exit on build failure
        # raise


# --- Main Orchestration Function ---

def create_book_pdf(input_md_file, output_pdf_file):
    """
    Orchestrates the PDF creation process: registers fonts, gets styles,
    extracts chapter data, runs Pass 1 to get page numbers, and runs Pass 2
    to build the final PDF with TOC.
    """
    global main_font # Need to set the global font name

    print(f"Starting PDF creation process for: {input_md_file}")

    # Setup: Register Fonts and Define Styles
    main_font = register_fonts() # Set the global font name
    styles = get_book_styles(main_font)

    # Extract Chapter Data from Input Markdown
    chapter_data = extract_chapter_data(input_md_file)
    if chapter_data is None: # Handle error during extraction
        print("Failed to extract chapter data. Exiting.")
        return

    # Run Pass 1: Get page numbers
    # Pass the initial first_content_page_num_physical to Pass 1, although Pass 1
    # doesn't strictly need it for its primary function (getting chapter page numbers
    # in the dummy doc). It's returned for consistency but not used to update the global.
    chapter_page_info, _ = run_pass1(input_md_file, styles)
    if not chapter_page_info: # Check if Pass 1 was successful in finding chapters
         print("Pass 1 did not find any chapter page numbers. TOC may be inaccurate or empty.")
         # Decide if you want to exit or proceed with potentially empty TOC

    # Run Pass 2: Build final PDF
    # Pass the determined first_content_page_num_physical (which is the initial global value)
    run_pass2(input_md_file, output_pdf_file, chapter_data, styles, chapter_page_info)

# --- Main Execution Block ---

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert a combined markdown book file into a PDF.")
    parser.add_argument("input_md", help="Path to the combined input markdown file.")
    parser.add_argument("output_pdf", help="Path for the output PDF file.")

    args = parser.parse_args()

    # Call the main orchestration function
    create_book_pdf(args.input_md, args.output_pdf)
