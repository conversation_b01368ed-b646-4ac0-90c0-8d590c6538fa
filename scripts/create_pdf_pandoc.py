#!/usr/bin/env python3

import os
import subprocess
import argparse
import sys

def create_pdf_with_pandoc(input_md, output_pdf, font_name="Times New Roman"):
    """
    Uses Pandoc to convert a Markdown file to PDF.
    Requires Pandoc and a LaTeX distribution (like MacTeX) to be installed.
    """
    print(f"Starting PDF creation with Pandoc for: {input_md}")
    print(f"Output file: {output_pdf}")
    print(f"Using font: {font_name}")

    # --- Pandoc Command Construction ---
    # Basic command
    pandoc_cmd = [
        'pandoc',
        input_md,
        '-o', output_pdf,
        '--toc',  # Generate Table of Contents
        '--number-sections', # Optional: Number sections in TOC and document
        # '--pdf-engine=xelatex', # Use xelatex for better font/unicode support (recommended)
        # '--pdf-engine=lualatex', # Alternative engine
    ]

    # --- Font Specification (using LaTeX variables) ---
    # For XeLaTeX or LuaLaTeX, setting 'mainfont' is common
    pandoc_cmd.extend(['-V', f'mainfont={font_name}'])
    # For pdflatex, you might need different variables or a template

    # --- Margins (Example using geometry package variable) ---
    pandoc_cmd.extend(['-V', 'geometry:margin=1in']) # Set all margins to 1 inch
    # You can specify individual margins: 'geometry:left=1.25in,right=1in,top=1in,bottom=1in'

    # --- Cover Image (Requires Pandoc >= 2.0) ---
    # Pandoc can often include the first image as a cover if structured correctly,
    # or you can specify it using metadata or a template.
    # For simplicity, we'll rely on Pandoc's default behavior or skip for now.
    # A more robust way involves a YAML metadata block in the MD file or a custom template.

    # --- Execute Pandoc ---
    print(f"\nExecuting command:\n{' '.join(pandoc_cmd)}\n")
    try:
        result = subprocess.run(pandoc_cmd, check=True, capture_output=True, text=True)
        print("Pandoc execution successful.")
        if result.stdout:
            print("Pandoc Output:\n", result.stdout)
        if result.stderr:
            print("Pandoc Errors/Warnings:\n", result.stderr) # LaTeX often outputs info here

        print(f"\nPDF created successfully: {output_pdf}")

        # Optional: Print file size
        if os.path.exists(output_pdf):
            file_size_bytes = os.path.getsize(output_pdf)
            file_size_kb = file_size_bytes / 1024
            file_size_mb = file_size_kb / 1024
            print(f"File size: {file_size_kb:.2f} KB ({file_size_mb:.2f} MB)")

    except FileNotFoundError:
        print("Error: 'pandoc' command not found.")
        print("Please ensure Pandoc is installed and in your system's PATH.")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"Error during Pandoc execution (Return Code: {e.returncode}):")
        print("Pandoc Output:\n", e.stdout)
        print("Pandoc Error Output:\n", e.stderr)
        print("\nCommon issues:")
        print("- LaTeX distribution (like MacTeX) might be missing or incomplete.")
        print("- Font specified might not be found by LaTeX.")
        print("- LaTeX template errors (if using a custom template).")
        print("- Check the Pandoc error output above for detailed LaTeX errors.")
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)

# --- Main Execution Block ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert a Markdown book file into a PDF using Pandoc.")
    parser.add_argument("input_md", help="Path to the input markdown file.")
    parser.add_argument("output_pdf", help="Path for the output PDF file.")
    parser.add_argument("-f", "--font", default="Times New Roman", help="Specify the main font for the PDF (must be recognized by LaTeX/system).")
    # parser.add_argument("--engine", default="xelatex", choices=['pdflatex', 'xelatex', 'lualatex'], help="PDF engine to use.") # Add if needed

    args = parser.parse_args()

    # Ensure input file exists
    if not os.path.exists(args.input_md):
        print(f"Error: Input markdown file not found at {args.input_md}")
        sys.exit(1)

    # Call the main function
    create_pdf_with_pandoc(args.input_md, args.output_pdf, font_name=args.font)
