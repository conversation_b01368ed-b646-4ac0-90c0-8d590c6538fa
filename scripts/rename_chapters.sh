#!/bin/zsh

# rename_chapters.sh - Renames chapter markdown files sequentially with 0-padding.
# Example: 010_intro.md -> 01_intro.md, 020_next.md -> 02_next.md

# --- Configuration ---
# Set the directory containing the chapter files relative to the script's location or use an absolute path.
# IMPORTANT: Make sure this path is correct before running!
TARGET_DIR="$1"
# --- End Configuration ---

# Check if the target directory exists
if [ ! -d "$TARGET_DIR" ]; then
  echo "Error: Target directory not found: $TARGET_DIR" >&2
  exit 1
fi

echo "Target directory: $(realpath "$TARGET_DIR")"
echo "Renaming files (Pass 1: Adding .tmp)..."

i=1 # Initialize counter

# --- Pass 1: Rename to temporary names ---
find "$TARGET_DIR" -maxdepth 1 -name '*.md' -print0 | sort -zV | while IFS= read -r -d $'\0' old_path; do
  # Extract the base filename (e.g., 010_the_cage.md)
  old_filename=$(basename "$old_path")

  # Extract the part after the first underscore (e.g., the_cage.md)
  # This assumes the format NUMBER_rest_of_name.md
  rest_of_name=$(echo "$old_filename" | sed 's/^[0-9][^_]*_//')

  # Format the counter with leading zero padding (e.g., 01, 02, ..., 10, 11)
  new_prefix=$(printf "%02d" "$i")

  # Construct the new filename and temporary path
  new_filename="${new_prefix}_${rest_of_name}"
  tmp_path="${TARGET_DIR}/${new_filename}.tmp" # Add .tmp extension

  # Rename to the temporary name
  cat "$old_path" | sed 's/^# [Cc]hapter[^:]*:/#/g' > "$tmp_path"
  rm "$old_path" # Remove the old file after copying

  # Increment counter
  i=$((i + 1))
done

echo "Pass 1 complete."
echo "Renaming files (Pass 2: Removing .tmp)..."

# --- Pass 2: Remove .tmp extension ---
find "$TARGET_DIR" -maxdepth 1 -name '*.md.tmp' -print0 | sort -zV | while IFS= read -r -d $'\0' tmp_path; do
  # Construct the final path by removing .tmp
  final_path="${tmp_path%.tmp}" # Use parameter expansion to remove suffix

  # Rename the file from .tmp to its final name
  mv -v "$tmp_path" "$final_path"
done

echo "Pass 2 complete."
echo "Renaming finished."
