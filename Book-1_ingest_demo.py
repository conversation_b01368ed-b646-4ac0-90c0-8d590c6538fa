import book_writer
import json
import sys
import traceback

def main():
    try:
        print("Starting Book-1 ingestion and outline generation...")
        
        # Ingest Book-1 and generate outlines
        book_dir = 'Book-1'
        print(f"Initializing BookGenerator...")
        generator = book_writer.BookGenerator()
        
        # Process the book
        print(f"Processing book directory: {book_dir}")
        result = book_writer.ingest_and_outline_book(book_dir, generator)
        
        # Print summary
        print("\n=== Summary ===")
        print(f"Processed {len(result['chapters'])} chapters")
        print(f"Generated book outline with {len(result['book_outline'])} characters")
        print(f"Total processing time: {result['timing']['total_seconds']:.2f} seconds")
        print(f" - Ingestion: {result['timing']['ingestion_seconds']:.2f} seconds ({(result['timing']['ingestion_seconds']/result['timing']['total_seconds'])*100:.1f}%)")
        print(f" - Chapter outlines: {result['timing']['chapter_outlines_seconds']:.2f} seconds ({(result['timing']['chapter_outlines_seconds']/result['timing']['total_seconds'])*100:.1f}%)")
        print(f" - Book outline: {result['timing']['book_outline_seconds']:.2f} seconds ({(result['timing']['book_outline_seconds']/result['timing']['total_seconds'])*100:.1f}%)")
        print(f"Estimated cost: ${generator.estimate_cost():.4f}")
        print(f"Overall generation speed: {result['timing']['chars_per_second']:.1f} chars/sec")
        
        # Save results to file
        output_file = f"{book_dir}_outline.json"
        print(f"\nSaving results to {output_file}...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2)
        print(f"Results saved successfully!")
        
        return 0
    except Exception as e:
        print(f"\n=== ERROR ===")
        print(f"An error occurred: {str(e)}")
        print("\nStacktrace:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())