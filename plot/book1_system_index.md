# The Obsidian Spire - Plot Organization System

This document serves as a central index for various tools used to organize and understand the complex plot of "The Obsidian Spire." Each linked document provides a different perspective on the narrative, allowing for easier management and development.

## Overview

"The Obsidian Spire" tells the story of <PERSON><PERSON>, an exiled healer, and <PERSON><PERSON><PERSON>, a conflicted commander, whose forbidden love ignites amidst rebellion on the sentient volcanic island of Aethelgard. Their bond challenges patriarchal oppression and <PERSON><PERSON><PERSON>'s tyranny, but also taps into unpredictable primal creation magic, leading to the unconventional birth of <PERSON><PERSON> and a dangerous dragon. The narrative explores themes of power, redemption, trauma, female empowerment, and the complexities of community and social dynamics under pressure, culminating in internal strife and the looming threat of mainland invasion.

## Plot Tools

*   [Hierarchical Outline](plot_hierarchical_outline.md) - A linear breakdown of the plot by acts and key events.
*   [Visual Diagram (Mermaid Code)](plot_diagram.mermaid) - Code for a flowchart illustrating plot progression and connections.
*   [Character Arcs](plot_character_arcs/index.md) - Documents detailing the individual journeys of key characters.
*   [Timeline](plot_timeline.md) - A chronological list of major events.
*   [Scene Summaries](plot_scene_summaries.md) - Brief descriptions of potential key scenes.
*   [Worldbuilding Bible](plot_worldbuilding_bible.md) - Details on the world, magic, history, and locations.
*   [Theme Tracking](plot_theme_tracking.md) - Notes on the exploration of major themes.

Navigate through the links above to explore the different aspects of the plot organization.