#!/usr/bin/env python3

import os
import re
import glob
import shutil
from datetime import datetime

"""
Implement Spiciness Guide for Obsidian Spire

This script integrates intimate scenes between <PERSON><PERSON> and <PERSON><PERSON><PERSON> from the design documents
into the appropriate chapters according to the five-stage progression outlined in the
spiciness guide.

The five stages are:
1. Initial Tension & Fascination (Ch 5-15)
2. Forced Proximity & Shifting Allegiance (Ch 16-22) 
3. Building Trust & Navigating the Wild (Ch 23-28)
4. Sacred Union & Conception (Ch 29)
5. Fractured Trust & Escalating Conflict (Ch 30-end)
"""

# Configuration
BOOK_DIR = "/Users/<USER>/Projects/ObsidianSpire/01_Book-The_Obsidian_Spire_Queendom/chapters"
DESIGNS_DIR = "/Users/<USER>/Projects/ObsidianSpire/02_Book-Legacy_of_Lies/design/intimate_scenes"
BACKUP_DIR = f"/Users/<USER>/Projects/ObsidianSpire/backups/chapters_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

# Stage definitions with chapter ranges and appropriate scenes to use
STAGES = {
    "1_initial_tension": {
        "chapters": range(5, 16),
        "scenes": ["boundary_marking.md"],
        "intensity": "low",
        "description": "Initial Tension & Fascination: Unwilling gravity with simmering tension"
    },
    "2_forced_proximity": {
        "chapters": range(16, 23),
        "scenes": ["sacred_revelations_lyra_cerys.md"],
        "intensity": "medium",
        "description": "Forced Proximity & Shifting Allegiance: Vulnerability during healing"
    },
    "3_building_trust": {
        "chapters": range(23, 29),
        "scenes": ["ritual_of_protection.md", "tender_devotions_lyra_cerys.md"],
        "intensity": "medium-high",
        "description": "Building Trust & Navigating the Wild: Leading to first kiss and tentative touches"
    },
    "4_sacred_union": {
        "chapters": [29],
        "scenes": ["sacred_revelations_lyra_cerys.md"],
        "intensity": "high",
        "description": "Sacred Union & Conception: The Hieros Gamos ritual - full sacred intimacy"
    },
    "5_fractured_trust": {
        "chapters": range(30, 40),
        "scenes": ["daily_rhythms_lyra_cerys.md"],
        "intensity": "medium",
        "description": "Fractured Trust & Escalating Conflict: Dealing with aftermath and consequences"
    }
}

def create_backup():
    """Create a backup of all chapter files before making changes"""
    print(f"Creating backup in {BACKUP_DIR}")
    os.makedirs(BACKUP_DIR, exist_ok=True)
    for chapter_file in glob.glob(os.path.join(BOOK_DIR, "*.md")):
        shutil.copy2(chapter_file, BACKUP_DIR)
    print("Backup completed")

def extract_scene_content(scene_file, intensity):
    """Extract appropriate content from a scene file based on intensity level"""
    with open(os.path.join(DESIGNS_DIR, scene_file), 'r') as file:
        content = file.read()
    
    # Remove the title
    content = re.sub(r'^# .+\n', '', content)
    
    # Extract paragraphs based on intensity
    paragraphs = content.split('\n\n')
    if intensity == "low":
        # For low intensity, extract tension and build-up but no explicit content
        selected_paragraphs = paragraphs[:min(5, len(paragraphs))]
    elif intensity == "medium":
        # For medium, include some sensual content but keep it limited
        middle_point = len(paragraphs) // 2
        selected_paragraphs = paragraphs[middle_point-3:middle_point+3]
    elif intensity == "medium-high":
        # For medium-high, more explicit but not the most intense
        middle_point = len(paragraphs) // 2
        selected_paragraphs = paragraphs[middle_point-5:middle_point+5]
    else:  # high
        # For high intensity, use the most intimate parts
        middle_point = len(paragraphs) // 2
        selected_paragraphs = paragraphs[middle_point-8:middle_point+8]
    
    # Filter out paragraphs to avoid overly explicit content if needed
    filtered_content = '\n\n'.join(selected_paragraphs)
    
    return filtered_content.strip()

def insert_intimate_scene(chapter_file, scene_content):
    """Insert intimate scene content into appropriate position in chapter"""
    with open(chapter_file, 'r') as file:
        chapter_content = file.read()
    
    # Find a good insertion point - typically after character dialogue or a scene transition
    # Look for patterns like paragraphs ending with quotes or scene breaks
    insertion_points = [m.end() for m in re.finditer(r'."(\n\n|\s*$)', chapter_content)]
    scene_breaks = [m.end() for m in re.finditer(r'\n\n\*\*\*\n\n', chapter_content)]
    insertion_points.extend(scene_breaks)
    
    if not insertion_points or max(insertion_points) < len(chapter_content) // 3:
        # If no good insertion points or they're all too early, insert at 2/3 of chapter
        insertion_point = len(chapter_content) * 2 // 3
    else:
        # Choose the latest viable insertion point
        viable_points = [p for p in insertion_points if p > len(chapter_content) // 3]
        insertion_point = max(viable_points) if viable_points else max(insertion_points)
    
    # Add scene break before intimate scene
    scene_transition = "\n\n***\n\n"
    
    # Insert the content
    updated_content = (
        chapter_content[:insertion_point] + 
        scene_transition + 
        scene_content + 
        scene_transition + 
        chapter_content[insertion_point:]
    )
    
    with open(chapter_file, 'w') as file:
        file.write(updated_content)

def process_chapter(chapter_num, stage_info):
    """Process a chapter to implement the appropriate level of intimacy"""
    # Find the chapter file
    chapter_files = glob.glob(os.path.join(BOOK_DIR, f"{chapter_num:02d}_*.md"))
    if not chapter_files:
        print(f"Warning: No file found for Chapter {chapter_num}")
        return
    
    chapter_file = chapter_files[0]
    
    # Select a scene file to use
    scene_file = stage_info["scenes"][chapter_num % len(stage_info["scenes"])]
    intensity = stage_info["intensity"]
    
    print(f"Processing Chapter {chapter_num}: {os.path.basename(chapter_file)}")
    print(f"  Using scene: {scene_file} with intensity: {intensity}")
    
    # Extract and modify content
    scene_content = extract_scene_content(scene_file, intensity)
    
    # Insert into chapter
    insert_intimate_scene(chapter_file, scene_content)
    print(f"  Updated Chapter {chapter_num} successfully")

def implement_spiciness_guide():
    """Main function to implement the spiciness guide across chapters"""
    print("Starting implementation of spiciness guide...")
    create_backup()
    
    # Process each chapter according to its stage
    for stage_name, stage_info in STAGES.items():
        print(f"\nProcessing {stage_info['description']}")
        for chapter_num in stage_info["chapters"]:
            process_chapter(chapter_num, stage_info)
    
    print("\nSpiciness guide implementation complete!")
    print(f"Original chapter files are backed up in {BACKUP_DIR}")

if __name__ == "__main__":
    implement_spiciness_guide()